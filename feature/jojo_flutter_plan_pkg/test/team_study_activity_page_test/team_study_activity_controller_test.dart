import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:jojo_flutter_plan_pkg/service/team_study_activity_api.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Mock classes

@GenerateMocks([TeamStudyActivityApi])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('TeamStudyActivityController Tests', () {
    late TeamStudyActivityController controller;
    late MockTeamStudyActivityApi mockApi;

    setUp(() {
      mockApi = MockTeamStudyActivityApi();
      controller = TeamStudyActivityController(api: mockApi);
    });

    group('fetchActivityInfo', () {
      test('should fetch activity info successfully', () async {
        // Arrange
        // final mockActivityModel = TeamStudyActivityModel(
        //   teamId: 123,
        //   status: 1,
        // );

        // when(mockApi.getTeamStudyActivityInfo(any, ‘’))
        //     .thenAnswer((_) async => mockActivityModel);

        // Act
        await controller.fetchActivityInfo();

        // Assert
        expect(controller.state.status, PageStatus.success);
        expect(controller.state.activityModel.teamId, 123);
        verify(mockApi.getTeamStudyActivityInfo(null, 'team')).called(1);
      });

      test('should handle error when fetching activity info fails', () async {
        // Arrange
        // when(mockApi.getTeamStudyActivityInfo(any, any))
        //     .thenThrow(Exception('Network error'));

        // Act
        await controller.fetchActivityInfo();

        // Assert
        expect(controller.state.status, PageStatus.error);
        verify(mockApi.getTeamStudyActivityInfo(null, 'team')).called(1);
      });

      test('should handle resource download failure', () async {
        // Arrange
        // final mockActivityModel = TeamStudyActivityModel(teamId: 123);
        // when(mockApi.getTeamStudyActivityInfo(any, any))
        //     .thenAnswer((_) async => mockActivityModel);

        // Act
        await controller.fetchActivityInfo();

        // Assert
        expect(controller.state.status, PageStatus.error);
      });
    });

    group('canShowEnergyAndUserInfo', () {
      test('should return true when in form team and step is energyShow', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 0); // inFormTeam
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.energyShow,
        ));

        // Act
        final result = controller.canShowEnergyAndUserInfo();

        // Assert
        expect(result, true);
      });

      test('should return true when activity is in progress', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 2); // inProgress
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act
        final result = controller.canShowEnergyAndUserInfo();

        // Assert
        expect(result, true);
      });

      test('should return false when conditions are not met', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 0); // inFormTeam
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act
        final result = controller.canShowEnergyAndUserInfo();

        // Assert
        expect(result, false);
      });
    });

    group('spinePlayCompleted', () {
      test('should call stepCompleted for shipStop step', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.shipStop,
        ));

        // Act
        controller.spinePlayCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.shipIdle);
      });

      test('should call stepCompleted for shipIdle step', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.shipIdle,
        ));

        // Act
        controller.spinePlayCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.energyShow);
      });

      test('should not call stepCompleted for other steps', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.energyShow,
        ));
        final initialStepType = controller.state.stepType;

        // Act
        controller.spinePlayCompleted();

        // Assert
        expect(controller.state.stepType, initialStepType);
      });
    });

    group('stepCompleted', () {
      test('should advance to next step in guide list when in form team', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0), // inFormTeam
          stepType: TeamStudyActivityStepType.shipStop,
        ));

        // Act
        controller.stepCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.shipIdle);
      });

      test('should advance to next step in progress list when in progress', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 2), // inProgress
          stepType: TeamStudyActivityStepType.energyShow,
        ));

        // Act
        controller.stepCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.shipStart);
      });

      test('should stay at last step when already at end', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0), // inFormTeam
          stepType: TeamStudyActivityStepType.explore,
        ));

        // Act
        controller.stepCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.explore);
      });
    });

    group('recvActivityReward', () {
      test('should call API when reward not yet received', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(
          teamId: 123,
          joinActivityRewardList: [
            TeamStudyActivityReward(), // not received
          ],
        );
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // when(mockApi.getMapRewards(any, any, any))
        //     .thenAnswer((_) async => {});

        // Act
        controller.recvActivityReward();

        // Assert
        verify(mockApi.getMapRewards(123, 2, [0])).called(1);
      });

      test('should not call API when reward already received', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(
          teamId: 123,
          joinActivityRewardList: [
            TeamStudyActivityReward(), // already received
          ],
        );
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act
        controller.recvActivityReward();

        // Assert
        // verifyNever(mockApi.getMapRewards(any, any, any));
      });
    });

    group('recvEnergy', () {
      test('should call API with correct parameters', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final member = TeamStudyActivityMember(memberId: 456);
        // when(mockApi.getMapRewards(any, any, any))
        //     .thenAnswer((_) async => {});

        // Act
        controller.recvEnergy(member);

        // Assert
        verify(mockApi.getMapRewards(123, 1, [456])).called(1);
      });

      test('should handle API error gracefully', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final member = TeamStudyActivityMember(memberId: 456);
        // when(mockApi.getMapRewards(any, any, any))
        //     .thenThrow(Exception('API error'));

        // Act & Assert (should not throw)
        expect(() => controller.recvEnergy(member), returnsNormally);
      });
    });

    group('getBeginGuideType', () {
      test('should return shipEnter for form team without local guide', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        final activityModel = TeamStudyActivityModel(status: 0); // inFormTeam

        // Act
        final result = await controller.getBeginGuideType(activityModel);

        // Assert
        expect(result, TeamStudyActivityStepType.shipEnter);
      });

      test('should return energyShow for activity in progress', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 2); // inProgress

        // Act
        final result = await controller.getBeginGuideType(activityModel);

        // Assert
        expect(result, TeamStudyActivityStepType.energyShow);
      });

      test('should return addPartner when reward already received', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({
          TeamStudyActivityController.TeamStudyActivityGuideKey:
              TeamStudyActivityStepType.shipEnter.index,
        });
        final activityModel = TeamStudyActivityModel(
          status: 0, // inFormTeam
          joinActivityRewardList: [
            TeamStudyActivityReward(), // already received
          ],
        );

        // Act
        final result = await controller.getBeginGuideType(activityModel);

        // Assert
        expect(result, TeamStudyActivityStepType.addPartner);
      });
    });
    group('setGuideType', () {
      test('should update state with new guide type', () {
        // Arrange
        final initialStepType = TeamStudyActivityStepType.shipEnter;
        final newStepType = TeamStudyActivityStepType.shipStop;

        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: initialStepType,
        ));

        // Act
        controller.setGuideType(newStepType);

        // Assert
        expect(controller.state.stepType, newStepType);
      });

      test('should update input state when provided', () {
        // Arrange
        final inputState = TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.shipEnter,
        );
        final newStepType = TeamStudyActivityStepType.shipStop;

        // Act
        controller.setGuideType(newStepType, inputState: inputState);

        // Assert
        expect(inputState.stepType, newStepType);
      });

      test('should handle openTreasure step type', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(
          teamId: 123,
          joinActivityRewardList: [],
        );
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act
        controller.setGuideType(TeamStudyActivityStepType.openTreasure);

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.openTreasure);
      });
    });

    group('saveGuideTypeToLocal', () {
      test('should save guide type to SharedPreferences', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        final guideType = TeamStudyActivityStepType.shipStop;

        // Act
        await controller.saveGuideTypeToLocal(guideType);

        // Assert
        final prefs = await SharedPreferences.getInstance();
        final savedIndex = prefs.getInt(TeamStudyActivityController.TeamStudyActivityGuideKey);
        expect(savedIndex, guideType.index);
      });
    });

    group('getLocalGuideType', () {
      test('should return saved guide type from SharedPreferences', () async {
        // Arrange
        final expectedType = TeamStudyActivityStepType.shipStop;
        SharedPreferences.setMockInitialValues({
          TeamStudyActivityController.TeamStudyActivityGuideKey: expectedType.index,
        });

        // Act
        final result = await controller.getLocalGuideType();

        // Assert
        expect(result, expectedType);
      });

      test('should return default guide type when no saved value', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await controller.getLocalGuideType();

        // Assert
        expect(result, TeamStudyActivityStepType.shipEnter);
      });
    });

    group('requestinvitations', () {
      test('should call API with correct team ID', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // final mockResponse = TeamInvitationListResponse(
        //   inviteList: [],
        //   code: 200,
        // );
        // when(mockApi.getTeamInvitations(any))
        //     .thenAnswer((_) async => mockResponse);

        // Act
        final result = await controller.requestinvitations();

        // Assert
        // expect(result, mockResponse);
        verify(mockApi.getTeamInvitations(123)).called(1);
      });

      test('should handle API error', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // when(mockApi.getTeamInvitations(any))
        //     .thenThrow(Exception('API error'));

        // Act & Assert
        expect(() => controller.requestinvitations(), throwsException);
      });
    });

    group('inviteUsersToTeam', () {
      test('should call API with correct parameters', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final inviteUserIds = [456, 789];
        // when(mockApi.inviteUsersToTeam(any, any))
        //     .thenAnswer((_) async => {});

        // Act
        await controller.inviteUsersToTeam(inviteUserIds);

        // Assert
        final expectedBody = {
          "teamId": 123,
          "inviteUserIds": inviteUserIds,
        };
        verify(mockApi.inviteUsersToTeam(123, expectedBody)).called(1);
      });

      test('should handle API error', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final inviteUserIds = [456, 789];
        // when(mockApi.inviteUsersToTeam(any, any))
        //     .thenThrow(Exception('API error'));

        // Act & Assert
        expect(() => controller.inviteUsersToTeam(inviteUserIds), throwsException);
      });
    });
  });
}