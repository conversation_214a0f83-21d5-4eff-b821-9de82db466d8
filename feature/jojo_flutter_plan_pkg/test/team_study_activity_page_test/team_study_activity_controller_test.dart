import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:jojo_flutter_plan_pkg/service/team_study_activity_api.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/tools/team_study_resource_tool.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Mock classes
class MockTeamStudyActivityApi extends Mock implements TeamStudyActivityApi {}

@GenerateMocks([TeamStudyActivityApi])

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('TeamStudyActivityController Tests', () {
    late TeamStudyActivityController controller;
    late MockTeamStudyActivityApi mockApi;

    setUp(() {
      mockApi = MockTeamStudyActivityApi();
      controller = TeamStudyActivityController(api: mockApi);
    });

    group('canShowEnergyAndUserInfo', () {

      test('init test', () {
        // Arrange
        final ctrl = TeamStudyActivityController();
        // Assert
        expect(true, true);
      });

      test('should fetch activity info successfully', () async {
        await controller.fetchActivityInfo();
        // Assert
        expect(controller.state.status, PageStatus.success);
      });

      test('should return true when in form team and step is energyShow', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 0); // inFormTeam
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.energyShow,
        ));

        // Act
        final result = controller.canShowEnergyAndUserInfo();

        // Assert
        expect(result, true);
      });

      test('should return true when activity is in progress', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 2); // inProgress
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act
        final result = controller.canShowEnergyAndUserInfo();

        // Assert
        expect(result, true);
      });

      test('should return false when conditions are not met', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 0); // inFormTeam
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act
        final result = controller.canShowEnergyAndUserInfo();

        // Assert
        expect(result, false);
      });
    });

    group('spinePlayCompleted', () {
      test('should call stepCompleted for shipStop step', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.shipStop,
        ));

        // Act
        controller.spinePlayCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.shipIdle);
      });

      test('should call stepCompleted for shipIdle step', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.shipIdle,
        ));

        // Act
        controller.spinePlayCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.energyShow);
      });

      test('should not call stepCompleted for other steps', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.energyShow,
        ));
        final initialStepType = controller.state.stepType;

        // Act
        controller.spinePlayCompleted();

        // Assert
        expect(controller.state.stepType, initialStepType);
      });
    });

    group('stepCompleted', () {
      test('should advance to next step in guide list when in form team', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0), // inFormTeam
          stepType: TeamStudyActivityStepType.shipStop,
        ));

        // Act
        controller.stepCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.shipIdle);
      });

      test('should advance to next step in progress list when in progress', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 2), // inProgress
          stepType: TeamStudyActivityStepType.energyShow,
        ));

        // Act
        controller.stepCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.shipStart);
      });

      test('should stay at last step when already at end', () {
        // Arrange
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0), // inFormTeam
          stepType: TeamStudyActivityStepType.explore,
        ));

        // Act
        controller.stepCompleted();

        // Assert
        expect(controller.state.stepType, TeamStudyActivityStepType.explore);
      });
    });

    group('recvActivityReward', () {
      test('should not call API when reward already received', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(
          teamId: 123,
          joinActivityRewardList: [
            TeamStudyActivityReward(), // already received
          ],
        );
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act & Assert (should not throw)
        expect(() => controller.recvActivityReward(), returnsNormally);
      });
    });

    group('getBeginGuideType', () {
      test('should return energyShow for activity in progress', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(status: 2); // inProgress

        // Act
        final result = await controller.getBeginGuideType(activityModel);

        // Assert
        expect(result, TeamStudyActivityStepType.energyShow);
      });
    });

    group('setGuideType', () {
      test('should update state with new guide type', () {
        // Arrange
        final initialStepType = TeamStudyActivityStepType.shipEnter;
        final newStepType = TeamStudyActivityStepType.shipStop;
        
        controller.emit(TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: initialStepType,
        ));

        // Act
        controller.setGuideType(newStepType);

        // Assert
        expect(controller.state.stepType, newStepType);
      });

      test('should update input state when provided', () {
        // Arrange
        final inputState = TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(status: 0),
          stepType: TeamStudyActivityStepType.shipEnter,
        );
        final newStepType = TeamStudyActivityStepType.shipStop;

        // Act
        controller.setGuideType(newStepType, inputState: inputState);

        // Assert
        expect(inputState.stepType, newStepType);
      });
    });

    group('saveGuideTypeToLocal', () {
      test('should save guide type to SharedPreferences', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        final guideType = TeamStudyActivityStepType.shipStop;

        // Act
        await controller.saveGuideTypeToLocal(guideType);

        // Assert
        final prefs = await SharedPreferences.getInstance();
        final savedIndex = prefs.getInt(TeamStudyActivityController.TeamStudyActivityGuideKey);
        expect(savedIndex, guideType.index);
      });
    });

    group('getLocalGuideType', () {
      test('should return saved guide type from SharedPreferences', () async {
        // Arrange
        final expectedType = TeamStudyActivityStepType.shipStop;
        SharedPreferences.setMockInitialValues({
          TeamStudyActivityController.TeamStudyActivityGuideKey: expectedType.index,
        });

        // Act
        final result = await controller.getLocalGuideType();

        // Assert
        expect(result, expectedType);
      });

      test('should return default guide type when no saved value', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await controller.getLocalGuideType();

        // Assert
        expect(result, TeamStudyActivityStepType.shipEnter);
      });
    });
    group('recvEnergy', () {
      test('should handle member with valid ID', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final member = TeamStudyActivityMember(memberId: 456);

        // Act & Assert (should not throw)
        expect(() => controller.recvEnergy(member), returnsNormally);
      });

      test('should handle member with null ID', () {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final member = TeamStudyActivityMember(memberId: null);

        // Act & Assert (should not throw)
        expect(() => controller.recvEnergy(member), returnsNormally);
      });
    });

    group('inviteUsersToTeam', () {
      test('should handle empty invite list', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final inviteUserIds = <int>[];

        // Act & Assert (should not throw)
        expect(() => controller.inviteUsersToTeam(inviteUserIds), returnsNormally);
      });

      test('should handle multiple invite IDs', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        final inviteUserIds = [456, 789, 101112];

        // Act & Assert (should not throw)
        expect(() => controller.inviteUsersToTeam(inviteUserIds), returnsNormally);
      });
    });

    group('requestinvitations', () {
      test('should handle team with valid ID', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: 123);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act & Assert (should not throw)
        expect(() => controller.requestinvitations(), returnsNormally);
      });

      test('should handle team with null ID', () async {
        // Arrange
        final activityModel = TeamStudyActivityModel(teamId: null);
        controller.emit(TeamStudyActivityState(
          activityModel: activityModel,
          stepType: TeamStudyActivityStepType.shipEnter,
        ));

        // Act & Assert (should not throw)
        expect(() => controller.requestinvitations(), returnsNormally);
      });
    });

    group('Controller State Management', () {
      test('should initialize with correct default state', () {
        // Act
        final initialState = controller.state;

        // Assert
        expect(initialState.status, PageStatus.loading);
        expect(initialState.stepType, TeamStudyActivityStepType.shipEnter);
        expect(initialState.activityModel, isNotNull);
      });

      test('should emit new state correctly', () {
        // Arrange
        final newState = TeamStudyActivityState(
          activityModel: TeamStudyActivityModel(teamId: 999),
          stepType: TeamStudyActivityStepType.shipStop,
          status: PageStatus.success,
        );

        // Act
        controller.emit(newState);

        // Assert
        expect(controller.state.activityModel.teamId, 999);
        expect(controller.state.stepType, TeamStudyActivityStepType.shipStop);
        expect(controller.state.status, PageStatus.success);
      });
    });

    group('Guide Step Lists', () {
      test('should have correct guide step list', () {
        // Assert
        expect(controller.guideStepList.length, 11);
        expect(controller.guideStepList.first, TeamStudyActivityStepType.shipStop);
        expect(controller.guideStepList.last, TeamStudyActivityStepType.explore);
      });

      test('should have correct progress step list', () {
        // Assert
        expect(controller.progressStepList.length, 5);
        expect(controller.progressStepList.first, TeamStudyActivityStepType.energyShow);
        expect(controller.progressStepList.last, TeamStudyActivityStepType.shipIdleAgain);
      });
    });
  });
}
