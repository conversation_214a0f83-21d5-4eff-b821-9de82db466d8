name: jojo_flutter_plan_pkg
description: plan.
version: 0.0.9
homepage:
environment:
  sdk: ">=2.17.6 <4.0.0"
  flutter: ">=1.17.0"

scripts:
  # 启动web开发环境
  start:dev: dart run jojo_flutter_base:start_web ENV_NAME=dev ENV_BASE=/read/plan/
  start:fat: dart run jojo_flutter_base:start_web ENV_NAME=fat ENV_BASE=/read/plan/
  # 打包web环境
  build: dart run jojo_flutter_base:build_web
  # 本地测试打包web环境
  debugBuild: dart run jojo_flutter_base:build_web ENV_NAME=dev ENV_BASE=/read/plan/ CDN_DOMAIN=https://jojopublicfat.jojoread.com CDN_PREFIX=/jojoread/flutter-accompany/ CI_TAG_NAME=v0.113.15
  # 生成静态资源路径
  assets: dart run jojo_flutter_base:gen_assets
  # 生成model代码
  json2model: flutter packages pub run build_runner build --delete-conflicting-outputs

dependencies:
  collection:
  date_format:
  webview_flutter: 4.2.4
  flutter_pickers: 2.1.9
  jojo_flutter_base:
    path: ../../core/jojo_flutter_base
  video_player:
    hosted: https://pub.xjjj.co/
    version: 2.7.6
  spine_flutter:
    hosted: https://pub.xjjj.co/
    version: ^4.2.36
  freezed_annotation: ^2.4.1
  archive: ^3.4.2
  path: ^1.8.2
  video_player_platform_interface: ^6.2.1
  cached_network_image: ^3.2.3
  flutter_easyrefresh: ^2.2.2

  flutter:
    sdk: flutter
  lottie: ^2.3.0
  shared_preferences: ^2.2.2
  preload_page_view: ^0.1.6
  flutter_lifecycle_aware: ^0.0.3
  retrofit:
  shimmer: ^3.0.0
  extended_tabs:
    hosted: https://pub.xjjj.co/
    version: ^4.0.5
  tangram_common_jojo:
    path: ../../core/tangram_common_jojo
  scrollable_positioned_list: ^0.3.8
  fl_chart: ^0.62.0
  flutter_svg: 1.1.6
  uuid: ^3.0.5
  jojo_flutter_download_plugin:
    hosted: https://pub.xjjj.co/
    version: 0.0.11
  flutter_cache_manager: 3.3.1

dev_dependencies:
  analyzer:
  build_runner:
  json_serializable:
  flutter_lints: ^2.0.0
  freezed:
  crypto: 3.0.2
  retrofit_generator: 5.0.0+1
  bloc_test: ^9.0.0
  flutter_test:
    sdk: flutter
  tangram_dev_tools:
    hosted: https://pub.xjjj.co/
    version: 0.0.7
  mockito: ^5.3.0


dependency_overrides:
  video_player:
    hosted: https://pub.flutter-io.cn
    version: any
  # video_player_android:
  #   hosted: https://pub.xjjj.co
  #   version: 2.3.22  
  # audioplayers_darwin:
  #   hosted: https://pub.xjjj.co
  #   version: 3.0.1
  # sqflite:
  #   hosted: https://pub.xjjj.co
  #   version: 2.2.10
  # image_picker_ios:
  #   hosted: https://pub.xjjj.co
  #   version: 0.8.9
  # shared_preferences_foundation:
  #   hosted: https://pub.xjjj.co
  #   version: 2.3.4
  # path_provider_foundation:
  #   hosted: https://pub.xjjj.co
  #   version: 2.3.1
  # image_cropper:
  #   hosted: https://pub.xjjj.co
  #   version: 4.0.2
  # webview_flutter_wkwebview:
  #   hosted: https://pub.xjjj.co
  #   version: 3.9.6
  # package_info_plus:
  #   hosted: https://pub.xjjj.co
  #   version: 2.0.1
  # image_picker_android: 0.8.6  

#  jojo_bridge_constraint:
#    hosted: https://pub.xjjj.co/
#    version: ^1.1.7 #这个和客户端版本对应，不能加^，鸿蒙现在改动比较频繁，先加上

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/tangram/
    - assets/img/
    - assets/img/recognition/
    - assets/img/share_action/
    - assets/img/comment_list/
    - assets/img/plan_continuology/
    - assets/img/plan_image/
    - assets/img/plan_harmonyos/
    - assets/img/plan_pure_enjoy/
    - assets/img/teacher_service/
    - assets/img/commendation_list/
    - assets/img/honor/
    - assets/img/course_task/
    - assets/img/full_screen_video/
    - assets/img/continue_study/
    - assets/svg/
    - assets/svg/teacher_service/
    - assets/svg/course_task/
    - assets/svg/drainage_course/
    - assets/svg/plan_home/
    - assets/svg/my_partners/
    - assets/audio/
    - assets/audio/finish_course/
    - assets/audio/medal_audios/
    - assets/lottie/
    - assets/spine/
    - assets/spine/finish_course/jojo/
    - assets/spine/finish_course/jojoOld/
    - assets/spine/finish_course/medal/
    - assets/spine/finish_course/progress/
    - assets/spine/finish_course/lvdou/
    - assets/spine/finish_course/milestone/
    - assets/img/incentive_module/
    - assets/img/medals/
    - assets/img/personal_home_module/
    - assets/img/course_session_list/
    - assets/svg/course_session_list/
    - assets/img/finish_course_settle_accounts/
    - assets/img/task_center/
    - assets/img/learning_incentives/
    - assets/svg/learning_incentives/
    - assets/img/plan_find_partner/
    - assets/img/partner_message/
    - assets/img/question_bank_list/
    - assets/img/ai/jiaojiao/
    - assets/audio/my_achievements/
    - assets/img/baby_profile/
    - assets/svg/baby_profile/
    - assets/img/team_study/


    

  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    # - family: MohrRounded
    #   fonts:
    #     - asset: /packages/jojo_flutter_design/assets/fonts/MohrRounded-Black.ttf
    #     - asset: /packages/jojo_flutter_design/assets/fonts/MohrRounded-BlackIt.ttf
    #       style: normal
    #       weight: 700
    # - family: NotoEmoji
    #   fonts:
    #     - asset: assets/fonts/NotoColorEmoji-Regular.ttf
    #       weight: 400
    - family: MohrRounded
      fonts:
        - asset: assets/fonts/MohrRounded-Bold.ttf
          weight: 400
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true
  main_locale: zh_Hans
