

class AssetsSpine {
  static const SPINE_FINISH_COURSE_JOJO_ATLAS = "assets/spine/finish_course/jojo/drx_jili_jojo.atlas.txt";
  static const SPINE_FINISH_COURSE_JOJO_SKEL = "assets/spine/finish_course/jojo/drx_jili_jojo.skel.bytes";
  static const SPINE_FINISH_COURSE_JOJO_PROGRESS_ATLAS = "assets/spine/finish_course/jojo/ip_jojo_progress.atlas.txt";
  static const SPINE_FINISH_COURSE_JOJO_PROGRESS_SKEL = "assets/spine/finish_course/jojo/ip_jojo_progress.skel.bytes";
  static const SPINE_FINISH_COURSE_MEDAL_GOT_ATLAS = "assets/spine/finish_course/medal/medal_got.atlas.txt";
  static const SPINE_FINISH_COURSE_MEDAL_GOT_SKEL = "assets/spine/finish_course/medal/medal_got.skel.bytes";
  static const SPINE_FINISH_COURSE_MEDAL_UPGRADE_ATLAS = "assets/spine/finish_course/medal/medal_upgrade.atlas.txt";
  static const SPINE_FINISH_COURSE_MEDAL_UPGRADE_SKEL = "assets/spine/finish_course/medal/medal_upgrade.skel.bytes";
  static const SPINE_FINISH_COURSE_MEDAL_TX_LIGHT_LEVELUP_ATLAS = "assets/spine/finish_course/medal/tx_light_levelup.atlas.txt";
  static const SPINE_FINISH_COURSE_MEDAL_TX_LIGHT_LEVELUP_SKEL = "assets/spine/finish_course/medal/tx_light_levelup.skel.bytes";
  static const SPINE_FINISH_COURSE_ASSET_ADD_ATLAS = "assets/spine/finish_course/medal/asset_add.atlas.txt";
  static const SPINE_FINISH_COURSE_ASSET_ADD_SKEL = "assets/spine/finish_course/medal/asset_add.skel.bytes";
  static const SPINE_FINISH_COURSE_PROGRESS_LIGHT_ATLAS = "assets/spine/finish_course/progress/progress_light.atlas.txt";
  static const SPINE_FINISH_COURSE_PROGRESS_LIGHT_SKEL = "assets/spine/finish_course/progress/progress_light.skel.bytes";
  static const SPINE_FINISH_COURSE_LVDOU_ATLAS = "assets/spine/finish_course/lvdou/drx_jili_lvdou.atlas.txt";
  static const SPINE_FINISH_COURSE_LVDOU_SKEL = "assets/spine/finish_course/lvdou/drx_jili_lvdou.skel.bytes";
  static const SPINE_FINISH_COURSE_LVDOU_PROGRESS_ATLAS = "assets/spine/finish_course/lvdou/ip_lvdou_progress.atlas.txt";
  static const SPINE_FINISH_COURSE_LVDOU_PROGRESS_SKEL = "assets/spine/finish_course/lvdou/ip_lvdou_progress.skel.bytes";
  static const SPINE_FINISH_COURSE_MILESTONE_BOX_ATLAS = "assets/spine/finish_course/milestone/ip_box.atlas.txt";
  static const SPINE_FINISH_COURSE_MILESTONE_BOX_SKEL = "assets/spine/finish_course/milestone/ip_box.skel.bytes";
  static const SPINE_FINISH_COURSE_MILESTONE_PRIZE_BG_ATLAS = "assets/spine/finish_course/milestone/tx_prize.atlas.txt";
  static const SPINE_FINISH_COURSE_MILESTONE_PRIZE_BG_SKEL = "assets/spine/finish_course/milestone/tx_prize.skel.bytes";
  static const POP_FINGER_TIPS_ATLAS = "assets/spine/finger_tips.atlas.txt";
  static const POP_FINGER_TIPS_SKEL = "assets/spine/finger_tips.skel.bytes";
  static const POP_FINGER_TIPS_PNG = "assets/spine/finger_tips.png";
  static const SPINE_FLOWER_CLIKC_ATKAS = 'assets/spine/flower_click.atlas.txt';
  static const SPINE_FLOWER_CLIKC_SKEL = 'assets/spine/flower_click.skel.bytes';
  static const SPINE_FINISH_COURSE_JOJO_OLD_ATLAS = "assets/spine/finish_course/jojoOld/drx_jili_jojo.atlas.txt";
  static const SPINE_FINISH_COURSE_JOJO_OLD_SKEL = "assets/spine/finish_course/jojoOld/drx_jili_jojo.skel.bytes";


}
