import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';

import '../../../../common/config/config.dart';
import '../model/team_study_sea_map_model.dart';
import '../state.dart';
import '../view.dart';
import 'team_study_map_reward_widget.dart';

extension PointRewardsBuilder on TeamStudySeaMapViewState {
  /// 奖励节点
  Widget getMapRewardWidget(BuildContext context, int index) {
    var groupHeights = widget.state.rewardLayerGroupHeights ?? [];
    var width = getMapWidth();

    int layerIndex = index - 1;
    if (layerIndex < 0 || layerIndex > groupHeights.length - 1) {
      var offset = layerIndex < 0 ? topCacheOffset : cacheOffset;
      return SizedBox(
          height: offset.rdp, width: mapFit(width)); // 弥补上方-50的偏移
    }

    var heights = widget.state.rewardLayerGroupHeights ?? [];
    var pointCounts = widget.state.rewardLayerGroupPointCounts ?? [];
    var points = widget.state.getPointsForLayer(layerIndex, pointCounts);

    var height = groupHeights[layerIndex];
    var startY = widget.state.getStartHeightForLayer(layerIndex, heights);
    var endY = startY + height;

    List<Widget> widgets = [];
    for (var i = 0; i < points.length; i++) {
      var point = points[i];
      var pointState = widget.state.getPointState(point);
      var stateWidget = getPointStateWidget(point, pointState, startY);
      widgets.add(stateWidget);
    }

    return Container(
      // color: Color((Random().nextDouble() * 0xFFFFFF).toInt() << 0).withOpacity(0.3),
      clipBehavior: Clip.none,
      width: mapFit(width),
      height: mapFit(height),
      child: Stack(
        clipBehavior: Clip.none,
        children: widgets,
      ),
    );
  }

  Widget getPointWidget(
    TeamStudySeaMapPoint point,
    TeamStudySeaMapPointState? state,
    double startY,
  ) {
    var isFinish = (state?.isFinish ?? 0) == 1;
    double left = mapFit(point.rect?.x ?? 0);
    double top = mapFit((point.rect?.y ?? 0) - startY);
    double width = mapFit(point.rect?.w ?? 65);
    double height = mapFit(point.rect?.h ?? 65);

    // print('weich map point ${point.id}');
    return Positioned(
      left: left,
      top: top,
      width: width,
      height: height,
      child: getPointBgWidget(isFinish, state?.number ?? point.id ?? 0, width),
    );
  }

  Widget getPointBgWidget(
    bool isFinish,
    int index,
    double size,
  ) {
    TeamStudySeaMapPointRes imageRes =
        getMapResImage(isFinish ? 'done' : 'empty');
    return Stack(
      children: [
        Positioned.fill(
          child: Image.file(
            File(getResFilePath(imageRes.image ?? "")),
            fit: BoxFit.cover,
          ),
        ),
        Positioned.fill(
          child: isFinish
              ? Container(
                  padding: EdgeInsets.only(bottom: mapFit(5)),
                  alignment: Alignment.center,
                  child: ContinuousDaysFire(
                    continuousDays: index,
                    size: size,
                    hideBg: true,
                  ),
                )
              : Container(
                  padding: EdgeInsets.only(top: mapFit(3)),
                  alignment: Alignment.center,
                  child: Text(
                    '$index',
                    style: TextStyle(
                      color: HexColor("#B2B2B2"),
                      fontSize: mapFit(39),
                      fontWeight: FontWeight.w400,
                      fontFamily: 'MohrRounded_Bold',
                      package: 'jojo_flutter_base',
                    ),
                  ),
                ),
        ),
      ],
    );
  }

  /// 获取点状态
  /// 最近的未领取奖励 大气泡宝箱关
  /// 其他的未领取奖励 小气泡宝箱关
  /// 待领取奖励 光旋转摇晃宝箱
  /// 已领取奖励 小气泡宝箱开
  Widget getPointStateWidget(
    TeamStudySeaMapPoint point,
    TeamStudySeaMapPointState pointState,
    double startY,
  ) {
    if (pointState.nodeRewardType == TeamStudySeaMapRewardType.none) {
      return Container();
    }

    // print('weich map reward ${point.id}');
    String rewardImageKey = widget.state.getRewardImageKey(pointState);

    var rewardState = widget.state.getRewardState(pointState);
    bool showCheck = widget.state.getRewardShowCheck(rewardState);
    bool showLight = widget.state.getRewardShowLight(rewardState);
    String bubbleImageKey = widget.state.getRewardBubbleImageKey(rewardState);
    Rect? rect = widget.state.getPointRewardRect(point);

    var rewardRes = getMapResImage(rewardImageKey);
    var bubbleRes = getMapResImage(bubbleImageKey);
    var lightRes = getMapResImage('reward_light');
    var checkRes = getMapResImage('reward_check');

    return Positioned(
      left: mapFit(rect?.left ?? 0),
      top: mapFit((rect?.top ?? 0) - startY),
      width: mapFit(rect?.width ?? 0),
      height: mapFit(rect?.height ?? 0),
      child: TeamStudySeaMapRewardWidget(
        rewardState: rewardState,
        reward: rewardRes,
        bubble: bubbleRes,
        light: lightRes,
        check: checkRes,
        showCheck: showCheck,
        showLight: showLight,
        shakeController: shakeController,
        rotationController: rotationController,
        mapFit: mapFit,
        clickAction: () {
          rewardClick(point, pointState);
        },
      ),
    );
  }

  void pointsBuilderInit() {
    createLightAnimation();
  }

  /// 奖励节点动画
  void createLightAnimation() {
    shakeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 720),
    );

    startShakeAnimation();

    rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    );
    rotationController.repeat();
  }

  void startShakeAnimation() {
    shakeController.forward();
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        shakeController.forward().then((_) {
          shakeController.reset();
          startShakeAnimation();
        });
      }
    });
  }

  getBoatWidget(Rect rect, double startY) {
    var boat = widget.state.getBoatSpine();
    if (boat == null) {
      return Container();
    }

    if (boatWidget != null) {
      return boatWidget;
    }

    boatWidget = Positioned(
      left: mapFit(rect.left),
      top: mapFit(rect.top - startY),
      width: mapFit(rect.width),
      height: mapFit(rect.height),
      child: Image.file(
        File(getResFilePath(boat.image ?? "")),
        fit: BoxFit.cover,
      ),
    );
    return boatWidget;
  }
}
