import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:jojo_flutter_base/base.dart';

import '../controller.dart';
import '../model/team_study_sea_map_model.dart';
import '../state.dart';

class TeamStudySeaMapRewardWidget extends StatefulWidget {
  final TeamStudySeaMapRewardState rewardState;

  final TeamStudySeaMapPointRes reward;
  final TeamStudySeaMapPointRes bubble;
  final TeamStudySeaMapPointRes light;
  final TeamStudySeaMapPointRes check;

  final bool showCheck;
  final bool showLight;

  final AnimationController shakeController;
  final AnimationController rotationController;

  final double Function(double) mapFit;
  final void Function()? clickAction;

  const TeamStudySeaMapRewardWidget({
    super.key,
    required this.rewardState,
    required this.reward,
    required this.bubble,
    required this.showCheck,
    required this.showLight,
    required this.light,
    required this.check,
    required this.shakeController,
    required this.rotationController,
    required this.mapFit,
    this.clickAction,
  });

  @override
  State<TeamStudySeaMapRewardWidget> createState() =>
      _TeamStudySeaMapRewardWidgetState();
}

class _TeamStudySeaMapRewardWidgetState
    extends State<TeamStudySeaMapRewardWidget> {
  late TeamStudySeaMapController _controller;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = context.read<TeamStudySeaMapController>();
    _createShakeAnimation();
  }

  void _createShakeAnimation() {
    _shakeAnimation = TweenSequence<double>(
      [
        // 第一阶段：从0度向左旋转到6度
        _getTweenSequenceItem(0, 6, 1),
        // 第二阶段：从6度向右旋转到-6度
        _getTweenSequenceItem(6, -6, 2),
        _getTweenSequenceItem(-6, 6, 2),
        _getTweenSequenceItem(6, -6, 2),
        _getTweenSequenceItem(-6, 6, 2),
        _getTweenSequenceItem(6, -6, 2),
        // 第三阶段：从-6度回到0度
        _getTweenSequenceItem(-6, 0, 1),
      ],
    ).animate(widget.shakeController);
  }

  TweenSequenceItem<double> _getTweenSequenceItem(
    double begin,
    double end,
    double weight,
  ) {
    return TweenSequenceItem(
      tween: Tween(
        begin: begin * pi / 180,
        end: end * pi / 180,
      ).chain(CurveTween(curve: Curves.easeInOut)),
      weight: weight,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  String getResFilePath(String path) {
    return _controller.getResFilePath(path);
  }

  double mapFit(double value) {
    return widget.mapFit(value);
  }

  @override
  Widget build(BuildContext context) {
    Widget pointStateWidget = Container();
    if (widget.rewardState == TeamStudySeaMapRewardState.openable) {
      pointStateWidget = buildLightPointStateWidget(
        reward: widget.reward,
        bubble: widget.bubble,
      );
    } else if (widget.rewardState == TeamStudySeaMapRewardState.lockedNext) {
      pointStateWidget = buildNormalPointStateWidget(
        reward: widget.reward,
        bubble: widget.bubble,
      );
    } else if (widget.rewardState == TeamStudySeaMapRewardState.opened ||
        widget.rewardState == TeamStudySeaMapRewardState.locked) {
      pointStateWidget = buildSmallPointStateWidget(
        reward: widget.reward,
        bubble: widget.bubble,
        showCheck: widget.showCheck,
      );
    }
    return  GestureDetector(
      onTap: widget.clickAction,
      child: pointStateWidget,
    );
  }

  Widget buildLightPointStateWidget({
    required TeamStudySeaMapPointRes reward,
    required TeamStudySeaMapPointRes bubble,
  }) {
    /// 宝箱摇晃动画
    return Container(
      color: Colors.transparent,
      width: mapFit(173),
      height: mapFit(173),
      child: Stack(
        children: [
          Positioned.fill(
            child: Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: mapFit(21.5)),
              child: getImageComponent(bubble, 130, 130, 1),
            ),
          ),
          Positioned.fill(
            child: RotationTransition(
              // 光效旋转动画
              turns: widget.rotationController,
              child: getImageComponent(widget.light, 173, 173, 1),
            ),
          ),
          Positioned.fill(
            child: Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: mapFit(30)),
              child: AnimatedBuilder(
                // 宝箱摇晃动画
                animation: _shakeAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _shakeAnimation.value,
                    alignment: const Alignment(0.0, 0.5),
                    child: child,
                  );
                },
                child: getImageComponent(reward, 95, 95, 1),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildNormalPointStateWidget({
    required TeamStudySeaMapPointRes reward,
    required TeamStudySeaMapPointRes bubble,
  }) {
    return Container(
      color: Colors.transparent,
      width: mapFit(130),
      height: mapFit(134),
      child: Stack(
        children: [
          Positioned.fill(
            child: Container(
              alignment: Alignment.topCenter,
              child: getImageComponent(bubble, 130, 130, 1),
            ),
          ),
          Positioned.fill(
            child: Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: mapFit(12.5)),
              child: getImageComponent(reward, 95, 95, 1),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSmallPointStateWidget({
    required TeamStudySeaMapPointRes reward,
    required TeamStudySeaMapPointRes bubble,
    required bool showCheck,
  }) {
    return Container(
      color: Colors.transparent,
      width: mapFit(130),
      height: mapFit(134),
      child: Stack(
        children: [
          Positioned.fill(
            child: Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: mapFit(26)),
              child: getImageComponent(bubble, 104, 104, 1),
            ),
          ),
          Positioned.fill(
            child: Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: mapFit(36)),
              child: getImageComponent(reward, 76, 76, 1),
            ),
          ),
          showCheck
              ? Positioned(
                  right: mapFit(10),
                  top: mapFit(28),
                  child: getImageComponent(widget.check, 38, 38, 1),
                )
              : Container(),
        ],
      ),
    );
  }

  Widget getImageComponent(
    TeamStudySeaMapPointRes res,
    double width,
    double height,
    double scale,
  ) {
    return Image.file(
      File(getResFilePath(res.image ?? "")),
      fit: BoxFit.contain,
      width: mapFit(width) * scale,
      height: mapFit(height) * scale,
    );
  }
}
