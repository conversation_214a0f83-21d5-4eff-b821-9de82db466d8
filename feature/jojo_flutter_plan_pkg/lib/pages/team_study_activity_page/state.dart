import 'dart:math';

import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';

enum TeamStudyActivityStepType {
  /// 船入场
  shipEnter('船入场', 'bg_forward', 'boat_forward',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], 'welcome.mp3'),
  shipStop('船停止', 'bg_stop', 'boat_stop',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], ''),
  shipIdle('船待机', 'bg_idle', 'boat_idle',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], ''),
  energyShow('火苗展示', 'bg_idle', 'boat_idle',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], 'learn_drive.mp3'),
  shipStart('充满能量,出发', 'bg_start', 'boat_start',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], 'boat_start.mp3'),
  shipForward('船前进', 'bg_forward', 'boat_forward',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], 'learn_tip.mp3'),
  shipStopAgain('船再次停止', 'bg_stop', 'boat_stop',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], ''),
  shipIdleAgain('船再次待机', 'bg_idle', 'boat_idle',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], ''),
  findTreasure('发现宝箱', 'bg_idle', 'boat_idle',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], ''),
  openTreasure('打开宝箱', 'bg_idle', 'boat_idle',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], ''),
  addPartner('添加队员', 'bg_idle', 'boat_idle',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], ''),
  explore('探索知识', 'bg_idle', 'boat_idle',
      ['idle_01', 'idle_02', 'idle_03', 'idle_04', 'idle_05', 'idle_06'], '');

  const TeamStudyActivityStepType(this.desc, this.bgAnimationName,
      this.boatAnimationName, this.roleAnimationNames, this.audioName);

  final String desc;
  final String bgAnimationName; // 背景动画
  final String boatAnimationName; // 船动画
  final List<String> roleAnimationNames; // 人物动画
  final String audioName; // 音频

  // 添加随机方法获取roleAnimationNames中的某个值
  String getRandomRoleAnimationName() {
    if (roleAnimationNames.isEmpty) {
      return '';
    }
    // 如果只有一个元素，直接返回
    if (roleAnimationNames.length == 1) {
      return roleAnimationNames[0];
    }
    // 使用传入的seed或生成随机索引
    final random = Random();
    final index = random.nextInt(roleAnimationNames.length);
    return roleAnimationNames[index];
  }
}

class TeamStudyActivityState {
  PageStatus? status; //loading状态
  Exception? exception;

  TeamStudyActivityModel activityModel;
  TeamStudyActivityStepType stepType; // 当前步骤类型
  TeamStudyResourceConfig? pageConfig;

  int energyFlyCount = 0;// 飞的能量数量

  TeamStudyActivityState(
      {this.status, this.exception,required this.activityModel,required this.stepType, this.pageConfig});

  TeamStudyActivityState copyWith() {
    return TeamStudyActivityState(
        status: status,
        activityModel: activityModel,
        pageConfig: pageConfig,
        stepType: stepType,
        exception: exception);
  }

  setGuideType(TeamStudyActivityStepType guideType) {
    l.i('TeamStudyTag',
        'setGuideType: ${guideType.desc} bg: ${guideType.bgAnimationName} boat: ${guideType.boatAnimationName} role: ${guideType.getRandomRoleAnimationName()} auido: ${guideType.audioName}}');
    stepType = guideType;
  }
}


class AnimationItem {
  int? index;
  int? type;
  String name;

  AnimationItem({
    this.index,
    this.type,
    required this.name,
  });
}
