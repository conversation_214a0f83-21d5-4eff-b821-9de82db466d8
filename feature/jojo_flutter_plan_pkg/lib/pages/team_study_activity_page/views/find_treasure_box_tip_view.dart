import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/team_study_activity_const.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/tools/team_study_resource_tool.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/lottie.dart';
import 'package:lottie/lottie.dart';

class FindTreasureBoxTipView extends StatefulWidget {
  final VoidCallback onTap;

  const FindTreasureBoxTipView({
    Key? key,
    required this.onTap,
  }) : super(key: key);

  @override
  State<FindTreasureBoxTipView> createState() => _FindTreasureBoxTipViewState();
}

class _FindTreasureBoxTipViewState extends State<FindTreasureBoxTipView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  late Animation<double> _opacityAnimation;

  bool _showFingerGuide = false;
  bool _isCancelled = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _controller = AnimationController(
      duration:
          const Duration(milliseconds: TeamStudyConst.findTreasureBoxTipsTime),
      vsync: this,
    );

    // 创建位移动画（从下方屏幕外到当前位置）
    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0), // 从下方屏幕外开始
      end: Offset.zero, // 到达最终位置
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    // 创建透明度动画（从0到1）
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    // 启动动画
    int delayPlayTime = 1000;
    Future.delayed(Duration(milliseconds: delayPlayTime), () {
      _controller.forward();
    });

    // 一定时间没有操作显示手的动画
    int delayShowHand = TeamStudyConst.findTreasureBoxTipsTime +
        TeamStudyConst.fingerGuideTime +
        delayPlayTime;
    Future.delayed(Duration(milliseconds: delayShowHand), () {
      if (mounted && !_isCancelled) {
        _showFingerGuide = true;
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  _didTap() {
    _isCancelled = true;
    widget.onTap();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).size.height * 0.6,
      left: 20.rdp, // 设置左右边距
      right: 20.rdp, // 设置左右边距
      child: SlideTransition(
        position: _offsetAnimation,
        child: FadeTransition(
          opacity: _opacityAnimation,
          child: GestureDetector(onTap: _didTap, child: _contentWidget()),
        ),
      ),
    );
  }

  Widget _contentWidget() {
    return Container(
      height: 88.rdp,
      // margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.rdp),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧文案
          _descWidget(),

          SizedBox(
            // 宝箱
            width: 60.rdp,
            height: 60.rdp,
            child: Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                _treasureBoxWidget(),
                if (_showFingerGuide) _fingerGuideWidget(),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _descWidget() {
    return Expanded(
      child: Text(
        S.of(context).findTreasure,
        style: TextStyle(
          fontSize: 18.rdp,
          fontWeight: FontWeight.w500,
          color: HexColor('#404040'),
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _treasureBoxWidget() {
    final path =
        TeamStudyResourceTool.getImagePath('team_study_treasure_box.png');
    return Positioned(
        child: Image.file(
      File(path),
      width: 60.rdp,
      height: 60.rdp,
      fit: BoxFit.contain,
    ));
    // return Positioned(
    //     child: ImageAssetWeb(
    //   assetName: AssetsImg.TEAM_STUDY_TEAM_STUDY_TREASURE_BOX,
    //   width: 60.rdp,
    //   height: 60.rdp,
    //   package: Config.package,
    //   fit: BoxFit.contain,
    // ));
  }

  // 手的引导组件
  Widget _fingerGuideWidget() {
    return Positioned(
      left: 15.rdp, // 相对于左边界向右10
      top: 5.rdp, // 相对于上边界向下10
      child: Transform(
        transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
        alignment: Alignment.center,
        child: IgnorePointer(
          ignoring: true,
          child: SizedBox(
            width: 70.rdp,
            height: 70.rdp,
            child: Lottie.asset(
              AssetsLottie.YEAR_FINGER_POINT,
              package: RunEnv.package,
              repeat: true,
            ),
          ),
        ),
      ),
    );
  }
}
