import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/widget/alternative_image_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

import '../../../../static/img.dart';
import '../../../find_study_partner/model/find_study_partner_model.dart';
import '../../controller.dart';
import '../../models/team_study_activity_model.dart';

class InvitePartnerDialog extends StatefulWidget {
  final TeamStudyActivityController controller;
  final VoidCallback? onInviteAll;
  final Function(FindStudyPartnerModel)? onInvitePartner;
  final VoidCallback? onClose;

  const InvitePartnerDialog({
    super.key,
    required this.controller,
    this.onInviteAll,
    this.onInvitePartner,
    this.onClose,
  });

  @override
  State<InvitePartnerDialog> createState() => _InvitePartnerDialogState();

  /// 显示邀请伙伴弹窗的静态方法
  static void show({
    required TeamStudyActivityController controller,
    VoidCallback? onInviteAll,
    Function(FindStudyPartnerModel)? onInvitePartner,
    VoidCallback? onClose,
  }) {
    SmartDialog.show(
      builder: (context) => InvitePartnerDialog(
        controller: controller,
        onInviteAll: onInviteAll,
        onInvitePartner: onInvitePartner,
        onClose: onClose,
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withOpacity(0.7),
      clickMaskDismiss: false,
      backDismiss: true,
    );
  }
}

class _InvitePartnerDialogState extends State<InvitePartnerDialog> {
  PageStatus _pageStatus = PageStatus.loading;
  List<TeamInvitationUser> _inviteList = [];

  @override
  void initState() {
    super.initState();
    _loadInvitations();
  }

  /// 加载邀请列表数据
  Future<void> _loadInvitations() async {
    setState(() {
      _pageStatus = PageStatus.loading;
    });

    try {
      final response = await widget.controller.requestinvitations();
      setState(() {
        _inviteList = List<TeamInvitationUser>.from(response.inviteList ?? []);
        _pageStatus = PageStatus.success;
      });
    } catch (e) {
      setState(() {
        _pageStatus = PageStatus.error;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var mediaData = MediaQuery.of(context);
    var height = mediaData.viewPadding.bottom + 647.rdp;
    final top = 131.rdp;
    if (height > mediaData.size.height - top) {
      height = mediaData.size.height - top;
    }

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32.rdp),
          topRight: Radius.circular(32.rdp),
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: JoJoPageLoadingV25(
        status: _pageStatus,
        retry: () {
          _loadInvitations();
        },
        child: Column(
          children: [
            SizedBox(height: 28.rdp),
            _buildHeader(),
            _buildSubtitle(),
            Expanded(child: _buildPartnerList()),
            _buildBottomButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 30.rdp,
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Row(
        children: [
          Expanded(
            child: Center(
              child: Text(
                widget.controller.state.pageConfig?.invitePartnerExplore ?? '',
                style: context.textstyles.headingLargeEmphasis.pf.copyWith(
                  color: context.appColors.jColorGray6,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtitle() {
    return Container(
      height: 27.rdp,
      margin: EdgeInsets.only(top: context.dimensions.minimumSpacing.rdp),
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Text(
        widget.controller.state.pageConfig?.threePartnerGetReward ?? '',
        style: context.textstyles.heading.pf.copyWith(
          color: context.appColors.jColorGray5,
        ),
      ),
    );
  }

  Widget _buildPartnerList() {
    if (_inviteList.isEmpty) {
      return _buildEmptyView();
    }

    final mediumSpacing = context.dimensions.mediumSpacing.rdp;

    return Container(
      margin: EdgeInsets.only(top: mediumSpacing),
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: _inviteList.length,
        itemBuilder: (context, index) {
          final inviteUser = _inviteList[index];
          return Container(
            margin: EdgeInsets.only(bottom: mediumSpacing),
            child: _buildInviteUserItem(inviteUser),
          );
        },
      ),
    );
  }

  Widget _buildInviteUserItem(TeamInvitationUser inviteUser) {
    return Container(
      height: 113.rdp,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius:
            BorderRadius.circular(context.dimensions.largeCornerRadius.rdp),
        border: Border.all(
          color: context.appColors.jColorGray3,
          width: 1.rdp,
        ),
      ),
      child: Stack(
        children: [
          _buildItemBg(),
          _buildAvatar(inviteUser.dressImg ?? ""),
          _buildContinuousDaysWidget(inviteUser.dayCount ?? 0),
          _buildNameWidget(inviteUser.nickname ?? "", inviteUser.isPatner == 1),
          _buildStudyDays(inviteUser.learnDay ?? 0),
          _buildInviteUserButton(inviteUser),
        ],
      ),
    );
  }

  Widget _buildItemBg() {
    return Positioned(
      left: 0,
      top: 0,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.rdp),
        child: ImageAssetWeb(
          assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_BG,
          fit: BoxFit.contain,
          package: Config.package,
          height: 113.rdp,
          width: 233.rdp,
        ),
      ),
    );
  }

  Widget _buildAvatar(String avatar) {
    return Positioned(
      left: 16.rdp,
      top: 4.rdp,
      child: AlternativeImageWidget(
        imageUrl: avatar,
        displayWidth: 113.rdp,
        displayHeight: 100.rdp,
        displayConfig: ImageDisplayConfig.waist,
        hideDefaultHolder: true,
      ),
    );
  }

  Widget _buildContinuousDaysWidget(int continuousDays) {
    final size = 36.rdp;
    return Positioned(
      left: 28.rdp,
      bottom: 8.rdp,
      height: size,
      width: size,
      child: ContinuousDaysFire(continuousDays: continuousDays, size: size),
    );
  }

  Widget _buildNameWidget(String name, bool isMyPartner) {
    return Positioned(
      left: 165.rdp,
      top: context.dimensions.mediumSpacing.rdp,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            name,
            style: context.textstyles.bodyTextEmphasis.pf
                .copyWith(color: context.appColors.jColorGray6),
          ),
          if (isMyPartner)
            Padding(
              padding:
                  EdgeInsets.only(left: context.dimensions.minimumSpacing.rdp),
              child: Text(
                S.of(context).mutualCompanionship,
                style: context.textstyles.smallestText.pf
                    .copyWith(color: context.appColors.jColorGray4),
              ),
            )
        ],
      ),
    );
  }

  Widget _buildStudyDays(int days) {
    return Positioned(
      left: 165.rdp,
      top: 39.rdp,
      child: Text(
        S.of(context).studyDays(days),
        style: context.textstyles.remark.pf
            .copyWith(color: context.appColors.jColorGray4),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: ImageAssetWeb(
        assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_LIST_EMPTY,
        package: Config.package,
        height: 120.rdp,
        width: 120.rdp,
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.only(
          top: 36.rdp, left: 20.rdp, right: 20.rdp, bottom: 16.rdp),
      child: Column(
        children: [
          // 一键邀请按钮
          GestureDetector(
            onTap: _inviteAllUsers,
            child: Container(
              width: double.infinity,
              height: 44.rdp,
              decoration: BoxDecoration(
                color: context.appColors.mainColor,
                borderRadius: BorderRadius.circular(24.rdp),
              ),
              child: Center(
                child: Text(
                  S.of(context).inviteAll,
                  style: context.textstyles.headingEmphasis.pf
                      .copyWith(color: context.appColors.jColorYellow6),
                ),
              ),
            ),
          ),
          SizedBox(height: context.dimensions.smallSpacing.rdp),
          // 关闭弹窗按钮
          SizedBox(
            width: double.infinity,
            height: 44.rdp,
            child: GestureDetector(
              onTap: () {
                SmartDialog.dismiss();
                widget.onClose?.call();
              },
              child: Text(
                S.of(context).closeDialog,
                style: context.textstyles.heading.pf
                    .copyWith(color: context.appColors.jColorGray5),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInviteUserButton(TeamInvitationUser inviteUser) {
    final spaceing = context.dimensions.mediumSpacing;

    // 根据邀请状态显示不同的按钮
    if (inviteUser.isInvite == 1) {
      // 已邀请状态
      return Positioned(
        right: spaceing.rdp,
        bottom: spaceing.rdp,
        width: 108.rdp,
        height: 32.rdp,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(25.rdp),
            border: Border.all(color: context.appColors.jColorGray2, width: 1.rdp),
          ),
          child: Row(children: [
            SizedBox(width: context.dimensions.smallSpacing.rdp),
            SizedBox(
              width: 20.rdp,
              height: 20.rdp,
              child: SvgAssetWeb(
                assetName: AssetsSvg.HOURGLASS_ICON,
                package: RunEnv.package,
              ),
            ),
            SizedBox(width: context.dimensions.minimumSpacing.rdp),
            Text(
              S.of(context).waitPass,
              style: context.textstyles.remark.pf
                  .copyWith(color: context.appColors.jColorGray4),
            ),
          ]),
        ),
      );
    } else {
      // 可邀请状态
      return Positioned(
        right: spaceing.rdp,
        bottom: spaceing.rdp,
        width: 80.rdp,
        height: 32.rdp,
        child: GestureDetector(
          onTap: () => _inviteSingleUser(inviteUser),
          child: Container(
            decoration: BoxDecoration(
              color: context.appColors.jColorYellow4,
              borderRadius: BorderRadius.circular(25.rdp),
            ),
            child: Row(children: [
              SizedBox(width: context.dimensions.smallSpacing.rdp),
              SizedBox(
                width: 20.rdp,
                height: 20.rdp,
                child: SvgAssetWeb(
                  assetName: AssetsSvg.ADD_ICON,
                  package: RunEnv.package,
                ),
              ),
              SizedBox(width: context.dimensions.minimumSpacing.rdp),
              Text(
                S.of(context).invite,
                style: context.textstyles.remark.pf
                    .copyWith(color: context.appColors.jColorYellow6),
              ),
            ]),
          ),
        ),
      );
    }
  }

  /// 邀请单个用户
  Future<void> _inviteSingleUser(TeamInvitationUser inviteUser) async {
    if (inviteUser.userId == null) return;

    try {
      // 静默邀请
      widget.controller.inviteUsersToTeam([inviteUser.userId!]);

      // 邀请成功后更新本地状态
      setState(() {
        _inviteList = _inviteList.map((user) {
          if (user.userId == inviteUser.userId) {
            return user.copyWith(isInvite: 1);
          }
          return user;
        }).toList();
      });
    } catch (e) {
      l.i('邀请列表', '邀请失败: $e, userId:${inviteUser.userId}');
    }
  }

  /// 一键邀请所有可邀请用户
  Future<void> _inviteAllUsers() async {
    final canInviteUsers = _inviteList.where((user) => user.isInvite == 0).toList();

    if (canInviteUsers.isEmpty) {
      SmartDialog.dismiss();
      widget.onClose?.call();
      return;
    }

    final inviteUserIds = canInviteUsers
        .where((user) => user.userId != null)
        .map((user) => user.userId!)
        .toList();

    if (inviteUserIds.isEmpty) return;

    try {
      // 静默调用
      widget.controller.inviteUsersToTeam(inviteUserIds);
      setState(() {
        _inviteList = _inviteList.map((user) {
          if (user.isInvite == 0 && user.userId != null) {
            return user.copyWith(isInvite: 1);
          }
          return user;
        }).toList();
      });

      l.i('邀请列表','一键邀请成功！共邀请 ${canInviteUsers.length} 位用户, $canInviteUsers');
      SmartDialog.dismiss();
      widget.onInviteAll?.call();
    } catch (e) {
       l.i('邀请列表','一键邀请失败: $e');
    }
  }
}
