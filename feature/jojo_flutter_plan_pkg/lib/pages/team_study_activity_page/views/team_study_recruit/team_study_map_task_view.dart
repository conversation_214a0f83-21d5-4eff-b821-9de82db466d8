import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

import '../../../team_study/sea_map/widgets/team_study_map_entry_builder.dart';
import '../../state.dart';


class TeamStudyMapTaskView extends StatefulWidget {
  final TeamStudyActivityState state;
  final int? subjectType;
  final double topOffset;

  const TeamStudyMapTaskView({
    super.key,
    required this.state,
    required this.subjectType,
    required this.topOffset,
  });

  @override
  State<TeamStudyMapTaskView> createState() => _TeamStudyMapTaskViewState();
}

class _TeamStudyMapTaskViewState extends State<TeamStudyMapTaskView> {
  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Container(
        clipBehavior: Clip.none,
        padding: EdgeInsets.only(
          left: 20.rdp,
          right: 20.rdp,
          top: widget.topOffset,
        ),
        child: SingleChildScrollView(
          clipBehavior: Clip.none,
          child: Column(
            children: [
              TeamStudyMapEntry(
                current: widget.state.activityModel?.current ?? 0,
                total: widget.state.activityModel?.total ?? 0,
                showReward:
                    (widget.state.activityModel?.isUnReceiveReward ?? 0) == 1,
                rewardType: widget.state.activityModel?.nodeRewardType ?? 0,
                onTap: () {
                  var route =
                      'tinman-router://cn.tinman.jojoread/flutter/teamStudy/seaMapPage';
                  if (widget.subjectType != null) {
                    route = route + '?subjectType=${widget.subjectType}';
                  }
                  RunEnv.jumpLink(route);
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
