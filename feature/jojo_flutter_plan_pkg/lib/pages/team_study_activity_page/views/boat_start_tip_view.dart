import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/team_study_activity_const.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/tools/team_study_audio_play_tool.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/tools/team_study_resource_tool.dart';


// 船出发tips

class BoatStartTipView extends StatefulWidget {
  final Function finishCallback;
  final String audioName;
  const BoatStartTipView({super.key, required this.finishCallback,required this.audioName});

  @override
  State<BoatStartTipView> createState() => _BoatStartTipViewState();
}

class _BoatStartTipViewState extends State<BoatStartTipView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _appearAnimation; // 出现动画
  late Animation<Offset> _disappearAnimation; // 消失动画

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: TeamStudyConst.boatStartTipsTime),
      vsync: this,
    );

    _appearAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: const Offset(0.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.115, curve: Curves.easeOut), // 前50%时间执行出现动画
    ));

    _disappearAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.0),
      end: const Offset(1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.884, 1.0, curve: Curves.easeOut), // 后30%时间执行消失动画
    ));

    // 添加状态监听
    _controller.addStatusListener(_animationStatusListener);

    // 开始动画
    _startAnimation();
  }

  _startAnimation() async {
    await Future.delayed(const Duration(milliseconds: 1000), () {
      _controller.forward();
    });
  }

  // 动画状态监听器
  void _animationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      l.i('BoatStartTipView', '动画播放完成');
      widget.finishCallback();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          // 根据动画进度决定使用哪个动画
          if (_controller.value <= 0.5) {
            return SlideTransition(
              position: _appearAnimation,
              child: Align(
                alignment: Alignment.center,
                child: SizedBox(
                  width: 375.rdp,
                  height: 100.rdp,
                  child: _testImg(),
                ),
              ),
            );
          } else {
            return SlideTransition(
              position: _disappearAnimation,
              child: Align(
                alignment: Alignment.center,
                child: SizedBox(
                  width: 375.rdp,
                  height: 100.rdp,
                  child: _testImg(),
                ),
              ),
            );
          }
        },
      ),
    );
  }

  Widget _testImg() {
    final path = TeamStudyResourceTool.getImagePath('boat_start_tip.png');
    return Image.file(
      File(path),
      fit: BoxFit.contain,
    );
  }
}
