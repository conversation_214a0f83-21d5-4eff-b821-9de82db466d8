import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/team_study_activity_const.dart';

class UserInfoView extends StatefulWidget {
  final TeamStudyActivityMember member; // 等级
  final Alignment alignment;
  const UserInfoView({  
    Key? key,
    required this.member,
    required this.alignment,
  }) : super(key: key);

  @override
  State<UserInfoView> createState() => _UserInfoViewState();
}

class _UserInfoViewState extends State<UserInfoView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: TeamStudyConst.userInfoFadeInTime),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);

     Future.delayed(const Duration(milliseconds: TeamStudyConst.userInfoShowDelayTime), () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: widget.alignment,
      child: FadeTransition(
        opacity: _animation,
        child: Container(
          height: 26.rdp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.5.rdp),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Transform.translate(
                offset: Offset(-5.rdp, 0), // 向左偏移
                child: ContinuousDaysFire(
                  continuousDays: widget.member.dayCount ?? 0,
                  size: 30,
                ),
              ),
              Transform.translate(
                offset: Offset(-2.rdp, 0), // 向左偏移
                child: Text(
                  widget.member.nickname ?? '',
                  style: TextStyle(
                    fontSize: 12.rdp,
                    color: HexColor('#DBAF00'),
                    fontWeight: FontWeight.w400,
                  ),
                  maxLines: 1,
                ),
              ),
              SizedBox(
                width: 8.rdp,
              )
            ],
          ),
        ),
      ),
    );
  }
}
