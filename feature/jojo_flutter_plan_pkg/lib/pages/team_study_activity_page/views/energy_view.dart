import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/static/img.dart' as baseImg;
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/team_study_activity_const.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/tools/team_study_resource_tool.dart';
import 'package:jojo_flutter_plan_pkg/static/lottie.dart';
import 'package:lottie/lottie.dart';

class EnergyView extends StatefulWidget {
  final int energyCount;
  final Alignment alignment;
  final bool haveGuide;
  final Function(int t, int energyCount) collectCallback; // 飞的动画时长
  const EnergyView(
      {Key? key,
      required this.energyCount,
      required this.alignment,
      required this.haveGuide,
      required this.collectCallback})
      : super(key: key);

  @override
  State<EnergyView> createState() => _EnergyViewState();
}

class _EnergyViewState extends State<EnergyView> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  // 移动动画
  late AnimationController _entranceController;
  late Animation<double> _entranceScaleAnimation;
  late Animation<Offset> _entranceOffsetAnimation;

  bool _showFingerGuide = false;
  bool _isCancelled = false;

  @override
  void initState() {
    super.initState();

    // 初始化旋转动画控制器
    _rotationController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..repeat(); // 无限重复

    // 创建旋转动画
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_rotationController);

    // 初始化缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 创建初始缩放动画（0到1，时长300ms）
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOutBack,
    ));

    // 启动初始缩放动画
    Future.delayed(
        const Duration(milliseconds: TeamStudyConst.userInfoShowDelayTime), () {
      if (mounted) {
        _scaleController.forward();
      }
    });

    // 在初始动画完成后，开始循环脉冲动画
    _scaleController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 延迟执行以避免在监听器中直接修改状态
        Future.delayed(Duration.zero, () {
          if (mounted) {
            _startPulseAnimation();
          }
        });
      }
    });

    // 飞动画控制器（位移+缩放，时长400ms）
    _entranceController = AnimationController(
      duration: const Duration(milliseconds: TeamStudyConst.energyFlyTime),
      vsync: this,
    );

    // 创建入场缩放动画
    _entranceScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.4,
    ).animate(CurvedAnimation(
      parent: _entranceController,
      curve: Curves.easeOut,
    ));

    // 创建飞的动画位移动画
    _entranceOffsetAnimation = Tween<Offset>(
      begin: Offset.zero, // 移动到最终位置
      end: const Offset(0, -0.5), // 从下方一点位置开始
    ).animate(CurvedAnimation(
      parent: _entranceController,
      curve: Curves.easeOut,
    ));
  }

  void _startPulseAnimation() {
    // 重新配置缩放控制器用于脉冲动画
    _scaleController.dispose();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1600),
      vsync: this,
    );

    // 创建脉冲动画（1.0到1.5再回到1.0）
    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.1),
        weight: 50, // 前400ms: 1.0 -> 1.5
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.0),
        weight: 50, // 后400ms: 1.5 -> 1.0
      ),
    ]).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    // 启动脉冲动画并重复播放
    _scaleController.repeat();

    // 由于我们替换了动画对象，需要调用setState来确保UI更新
    setState(() {});

    // 一定时间没有操作显示手的动画
    if (widget.haveGuide) {
      Future.delayed(
          const Duration(milliseconds: TeamStudyConst.fingerGuideTime), () {
        if (mounted && !_isCancelled) {
          _showFingerGuide = true;
          setState(() {});
        }
      });
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  _clickEnergy() {
    l.i('TeamStudyTag', '点击了能量');
    _isCancelled = true;

    // 飞的动画
    _entranceController.forward();

    // 回调出去，把总动画总时间去抛出去
    widget.collectCallback(TeamStudyConst.energyAllTime, widget.energyCount);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _clickEnergy();
      },
      child: Align(
        alignment: widget.alignment,
        child: SlideTransition(
          position: _entranceOffsetAnimation,
          child: ScaleTransition(
            scale: _entranceScaleAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: SizedBox(
                width: 100,
                height: 100,
                child: Stack(
                  children: [
                    _bgWidget(),
                    _lightWidget(),
                    _energyBgWidget(), // 能量背景
                    _energyNumWidget(), // 能量数字
                    if (_showFingerGuide)
                      Positioned(
                        top: 25.rdp,
                        left: 30.rdp,
                        child: _fingerGuideWidget(),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _bgWidget() {
    final path = TeamStudyResourceTool.getImagePath('team_study_fire_bg3.png');
    return Positioned.fill(
      child: Image.file(
        File(path),
        fit: BoxFit.fill,
      ),
    );
  }

  Widget _lightWidget() {
    final path = TeamStudyResourceTool.getImagePath('team_study_fire_bg2.png');
    return Positioned.fill(
      child: Align(
        alignment: Alignment.center,
        child: RotationTransition(
          turns: _rotationAnimation,
          child: SizedBox(
            width: 83,
            height: 83,
            child: Image.file(
              File(path),
              fit: BoxFit.fill,
            ),
          ),
        ),
      ),
    );
  }

  Widget _energyBgWidget() {
    final path = TeamStudyResourceTool.getImagePath('team_study_fire_bg.png');
    return Positioned.fill(
      child: Align(
        alignment: Alignment.center,
        child: SizedBox(
          width: 45,
          height: 45,
          child: Image.file(
            File(path),
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }

  // 能量数量组件
  Widget _energyNumWidget() {
    return Positioned.fill(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: _buildNumberImages(
          widget.energyCount,
          16,
          9999,
        ),
      ),
    );
  }

  // hwScale 高宽比，高度/宽度 用数字图片最宽的图片尺寸来计算
  List<Widget> _buildNumberImages(int number, double height, int limitNum) {
    if (number > limitNum) {
      number = limitNum;
    }
    final map = {
      'k': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_K,
      'w': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_W,
      '0': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_0,
      '1': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_1,
      '2': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_2,
      '3': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_3,
      '4': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_4,
      '5': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_5,
      '6': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_6,
      '7': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_7,
      '8': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_8,
      '9': baseImg.AssetsImg.CONTINUOUS_DAYS_CONTINUOUS_DAYS_FIRE_9,
    };

    return number.toString().split('').map((String num) {
      return ImageAssetWeb(
        assetName: map[num] ?? "",
        height: height,
        package: 'jojo_flutter_base',
        fit: BoxFit.contain,
      );
    }).toList();
  }

  // 手的引导组件
  Widget _fingerGuideWidget() {
    return Transform(
      transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
      alignment: Alignment.center,
      child: IgnorePointer(
        ignoring: true,
        child: SizedBox(
            width: 60.rdp,
            height: 60.rdp,
            child: Lottie.asset(
              AssetsLottie.YEAR_FINGER_POINT,
              package: RunEnv.package,
              repeat: true,
            )),
      ),
    );
  }
}
