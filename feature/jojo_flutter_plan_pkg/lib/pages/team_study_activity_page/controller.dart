import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/tools/team_study_resource_tool.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_state.dart';
import 'package:jojo_flutter_plan_pkg/service/team_study_activity_api.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'state.dart';
import 'tools/team_study_audio_play_tool.dart';

class TeamStudyActivityController extends Cubit<TeamStudyActivityState> {
  TeamStudyActivityController({TeamStudyActivityApi? api})
      : api = api ?? teamStudyApi, // 在初始化列表中处理
        super(TeamStudyActivityState(
            status: PageStatus.loading,
            activityModel: TeamStudyActivityModel(),
            stepType: TeamStudyActivityStepType.shipEnter));

  final TeamStudyActivityApi api;
  static String TeamStudyActivityGuideKey = 'TeamStudyActivityGuideKey';

  // 引导的步骤集合
  final List<TeamStudyActivityStepType> guideStepList = [
    TeamStudyActivityStepType.shipStop,
    TeamStudyActivityStepType.shipIdle,
    TeamStudyActivityStepType.energyShow,
    TeamStudyActivityStepType.shipStart,
    TeamStudyActivityStepType.shipForward,
    TeamStudyActivityStepType.shipStopAgain,
    TeamStudyActivityStepType.shipIdleAgain,
    TeamStudyActivityStepType.findTreasure,
    TeamStudyActivityStepType.openTreasure,
    TeamStudyActivityStepType.addPartner,
    TeamStudyActivityStepType.explore,
  ];
  // 活动中的步骤集合
  final List<TeamStudyActivityStepType> progressStepList = [
    TeamStudyActivityStepType.energyShow,
    TeamStudyActivityStepType.shipStart,
    TeamStudyActivityStepType.shipForward,
    TeamStudyActivityStepType.shipStopAgain,
    TeamStudyActivityStepType.shipIdleAgain,
  ];

  // 获取活动信息
  fetchActivityInfo() async {
    final newState = state.copyWith();
    try {
      // 1 获取活动信息
      TeamStudyActivityModel activityModel =
          await api.getTeamStudyActivityInfo(null, 'team');
      l.i('TeamStudyTag', '活动正在组队中:${activityModel.inFormTeam()}');
      // 2 下载资源
      bool downloadSuccess =
          await TeamStudyResourceTool.downloadResource(activityModel);
      if (!downloadSuccess) {
        // 下载失败
        newState.status = PageStatus.error;
        return;
      }
      // 3 解析资源配置
      TeamStudyResourceConfig config =
          await TeamStudyResourceTool.parseResourceConfig();
      newState.pageConfig = config;

      // 4 更新数据
      newState.activityModel = activityModel;

      // 5 获取当前引导状态
      final type = await getBeginGuideType(activityModel);
      setGuideType(type, inputState: newState);

      newState.status = PageStatus.success;
    } catch (e) {
      newState.status = PageStatus.error;
    } finally {
      // 6 刷新UI
      emit(newState);
    }
  }

  // 是否能显示能量
  bool canShowEnergyAndUserInfo() {
    if (state.activityModel.inFormTeam() &&
        state.stepType == TeamStudyActivityStepType.energyShow) {
      return true;
    }
    if (state.activityModel.inProgress()) {
      return true;
    }
    return false;
  }

  // spine动画播放结束
  spinePlayCompleted() {
    if (state.stepType == TeamStudyActivityStepType.shipStop ||
        state.stepType == TeamStudyActivityStepType.shipIdle ||
        state.stepType == TeamStudyActivityStepType.shipForward ||
        state.stepType == TeamStudyActivityStepType.shipStopAgain ||
        state.stepType == TeamStudyActivityStepType.shipIdleAgain) {
      stepCompleted();
    }
  }

  // 当前步骤结束
  stepCompleted() {
    List<TeamStudyActivityStepType> list =
        state.activityModel.inFormTeam() ? guideStepList : progressStepList;

    final index = list.indexOf(state.stepType) + 1;
    TeamStudyActivityStepType newStepType = list.last;
    if (index < list.length) {
      newStepType = list[index];
    }
    setGuideType(newStepType);
  }

  // 领取活动奖励
  recvActivityReward() {
    if (state.activityModel.didGetReward()) {
      return;
    }
    try {
      int teamId = state.activityModel.teamId ?? 0;
      api.getMapRewards(teamId, 2, [0]);
    } catch (e) {
      l.i('TeamStudyTag', '领取活动宝箱失败:$e');
    }
  }

  // 收取能量
  recvEnergy(TeamStudyActivityMember member) {
    int teamId = state.activityModel.teamId ?? 0;
    int memberId = member.memberId ?? 0;
    try {
      api.getMapRewards(teamId, 1, [memberId]);
    } catch (e) {
      l.i('TeamStudyTag', '能量领取失败:$e');
    }
  }

  // 获取初始引导状态
  Future<TeamStudyActivityStepType> getBeginGuideType(
      TeamStudyActivityModel activityModel) async {
    // return TeamStudyActivityStepType.shipEnter;

    var guideType = TeamStudyActivityStepType.shipEnter;
    if (activityModel.inFormTeam()) {
      // 组队中

      guideType = await getLocalGuideType();
      // 已经领取过活动奖励
      if (activityModel.didGetReward()) {
        // 已经领取过奖励引导步骤
        if (guideType.index <= TeamStudyActivityStepType.openTreasure.index) {
          guideType = TeamStudyActivityStepType.addPartner;
        }
      }
    } else if (activityModel.inProgress()) {
      // 进行中

      guideType = TeamStudyActivityStepType.energyShow;
    }
    return guideType;
  }

  // 设置引导状态
  setGuideType(TeamStudyActivityStepType guideType,
      {TeamStudyActivityState? inputState}) {
    // 开宝箱
    if (guideType == TeamStudyActivityStepType.openTreasure) {
      // 领取奖励
      recvActivityReward();
      // 打开领取页面
      if (state.activityModel.didGetReward() == false) {
        final reward = state.activityModel.joinActivityRewardList!.first;
        final data = TreasureChestData(
            name: reward.name ?? '',
            num: reward.num ?? 1,
            desc: reward.desc ?? '',
            icon: reward.img ?? '');
        TreasureChestPage.show(
            data: data,
            onClose: () {
              stepCompleted();
            });
      }
    }

    if (inputState != null) {
      inputState.setGuideType(guideType);
    } else {
      final newState = state.copyWith();
      newState.setGuideType(guideType);
      emit(newState);
    }

    TeamStudyActivityState stateValue =  inputState ?? state;
    if (stateValue.activityModel.inFormTeam()) {// 组队中，播放音频，存引导状态到本地
      // 组队中，播放音频
      TeamStudyAudioPlayTool.playAudio(name: guideType.audioName);

      saveGuideTypeToLocal(guideType);
    }
  }

  /// 设置本地存储的引导状态
  saveGuideTypeToLocal(TeamStudyActivityStepType guideType) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setInt(TeamStudyActivityGuideKey, guideType.index);
  }

  // 获取本地存储的引导状态
  Future<TeamStudyActivityStepType> getLocalGuideType() async {
    final prefs = await SharedPreferences.getInstance();
    int guideTypeIndex = prefs.getInt(TeamStudyActivityGuideKey) ?? 0;
    return TeamStudyActivityStepType.values[guideTypeIndex];
  }

  Future<TeamInvitationListResponse> requestinvitations() async {
    int teamId = state.activityModel.teamId ?? 0;
    return await api.getTeamInvitations(teamId);
  }

  /// 邀请用户加入队伍
  Future<void> inviteUsersToTeam(List<int> inviteUserIds) async {
    int teamId = state.activityModel.teamId ?? 0;
    final body = {
      "teamId": teamId,
      "inviteUserIds": inviteUserIds,
    };
    await api.inviteUsersToTeam(teamId, body);
  }
}
