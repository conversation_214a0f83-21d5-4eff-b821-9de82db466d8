import 'package:freezed_annotation/freezed_annotation.dart';


import '../../team_study/sea_map/model/team_study_sea_map_model.dart';
part 'team_study_activity_model.freezed.dart';
part 'team_study_activity_model.g.dart';

@freezed
class TeamStudyActivityModel with _$TeamStudyActivityModel {
  factory TeamStudyActivityModel({
    int? teamId,
    int? status, //活动状态(0未解锁,1组队中,2进行中,3已结束)
    int? startTime,
    int? endTime,
    int? subjectType,
    String? subjectName,
    String? resource,
    String? mapResource,
    int? current,
    int? total,
    int? isUnReceiveReward,
    int? nodeRewardType,
    List<TeamStudyActivityReward>? joinActivityRewardList,
    List<TeamStudyActivityMember>? teamMemberList,
    List<TeamStudySeaMapPointState>? mapNodeList,
  }) = _TeamStudyActivityModel;
  factory TeamStudyActivityModel.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyActivityModelFromJson(json);
}

extension TeamStudyActivityModelExtension on TeamStudyActivityModel {
  // 是否已经领取活动奖励
  bool didGetReward() {
    if (joinActivityRewardList == null) {
      // 没有代表已领取
      return true;
    }
    for (var element in joinActivityRewardList!) {
      if (element.isReceive == 1) {
        // 1代表已领取
        return true;
      }
    }
    return false;
  }

  int getSelfId() {
    return teamMemberList
            ?.firstWhere((element) => element.isSelf == 1)
            .memberId ??
        0;
  }

  // 需要下载的资源
  List<String> needDownloadUrls() {
    List<String> urls = [];
    if (resource?.isNotEmpty ?? false) {
      urls.add(resource!);
    }

    // 添加teamMemberList里的dressList资源
    if (teamMemberList != null) {
      for (var member in teamMemberList!) {
        if (member.dressList != null) {
          for (var dress in member.dressList!) {
            if (dress.resource?.isNotEmpty ?? false) {
              urls.add(dress.resource!);
            }
          }
        }
      }
    }
    return urls;
  }
  
  // 是否组队中
  bool inFormTeam(){
    return status == 1;
  }

  bool inProgress(){
    return status == 2;
  }
}

@freezed
class TeamStudyActivityReward with _$TeamStudyActivityReward {
  factory TeamStudyActivityReward({
    int? id,
    int? type,
    int? num,
    String? name,
    String? desc,
    String? img,
    int? isReceive, // 1:是否领取(1是0否)
  }) = _TeamStudyActivityReward;
  factory TeamStudyActivityReward.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyActivityRewardFromJson(json);
}

@freezed
class TeamStudyActivityMember with _$TeamStudyActivityMember {
  factory TeamStudyActivityMember({
    int? memberId,
    String? photo,
    String? nickname,
    int? dayCount,
    int? isSelf,
    int? pendingCollectionEnergy,
    List<TeamStudyActivityMemberDress>? dressList,
  }) = _TeamStudyActivityMember;
  factory TeamStudyActivityMember.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyActivityMemberFromJson(json);
}

@freezed
class TeamStudyActivityMemberDress with _$TeamStudyActivityMemberDress {
  factory TeamStudyActivityMemberDress({
    String? resource,
  }) = _TeamStudyActivityMemberDress;

  factory TeamStudyActivityMemberDress.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyActivityMemberDressFromJson(json);
}

@freezed
class TeamStudyResourceConfig with _$TeamStudyResourceConfig {
  factory TeamStudyResourceConfig({
    String? topBgColor,
    String? bottomBgColor,
    List<TeamStudySpineResource>? animationLayers,
    String? boatStartTips,
	  String? teamStudyRecruitTitle,
  	String? teamStudyRecruitDetail,
  	String? teamStudyRecruitDetail2,
  	String? teamStudyRecruitTipTwoLeft,
  	String? teamStudyRecruitTipOneLeft,
  	String? teamStudyRecruitTipComplete,
    String? invitePartnerExplore,
    String? threePartnerGetReward,

  }) = _TeamStudyResourceConfig;
  factory TeamStudyResourceConfig.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyResourceConfigFromJson(json);
}

// 邀请列表响应模型
@freezed
class TeamInvitationListResponse with _$TeamInvitationListResponse {
  factory TeamInvitationListResponse({
    List<TeamInvitationUser>? inviteList,
  }) = _TeamInvitationListResponse;

  factory TeamInvitationListResponse.fromJson(Map<String, dynamic> json) =>_$TeamInvitationListResponseFromJson(json);
}

// 邀请用户信息模型
@freezed
class TeamInvitationUser with _$TeamInvitationUser {
  factory TeamInvitationUser({
    int? userId, // 用户id
    String? nickname, // 昵称
    String? dressImg, // 装扮图
    int? learnDay, // 累计学习天数
    int? dayCount, // 当前连续天数
    int? bestDayCount, // 历史连续天数
    int? isInvite, // 是否邀请(1是0否)
    int? isPatner, // 是否学伴(1是0否)
  }) = _TeamInvitationUser;

  factory TeamInvitationUser.fromJson(Map<String, dynamic> json) =>_$TeamInvitationUserFromJson(json);
}


@freezed
class TeamStudySpineResource with _$TeamStudySpineResource {
  factory TeamStudySpineResource({
    int? type,
    String? name,
    List<String>? personMount,
    
    
  }) = _TeamStudySpineResource;
  factory TeamStudySpineResource.fromJson(Map<String, dynamic> json) =>
      _$TeamStudySpineResourceFromJson(json);
}
