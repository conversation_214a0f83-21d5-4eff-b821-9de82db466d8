import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';

import 'team_study_resource_tool.dart';

class TeamStudyAudioPlayTool {
  static playAudio({required String name, Function? callback}) {
    if(name.isEmpty){
      return;
    }
    l.i('TeamStudyTag', '播放音频:$name');
    final path = TeamStudyResourceTool.getAudioPath(name);
    if (path.isEmpty && callback != null) {
      callback();
      return;
    }
    final palyer = AudioPlayer();
    if (callback != null) {
      palyer.onPlayerStateChanged.listen((PlayerState state) {
        if (state == PlayerState.completed) {
          callback();
        }
      });
    }
    final source = DeviceFileSource(path);
    palyer
        .play(source)
        .onError((error, stackTrace) => {l.i('TeamStudyTag', '音频播放失败:$name')});
  }
}
