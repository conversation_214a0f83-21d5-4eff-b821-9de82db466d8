import 'dart:convert';
import 'dart:io';

import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/spine_download_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';

enum TeamStudySpineType {
  seaBackground,    // 海背景
  boatBackground,   // 船背景
  boatForeground,   // 船前景
  seaForeground,    // 海前景
  character,        // 人物
}


class TeamStudyResourceTool {

  static Map<String,String> resourceMap = {};
  static String resourceUrl = '';

  // 下载资源
  static Future<bool> downloadResource(TeamStudyActivityModel activityModel) async {
    try {
      List<String> urlList = activityModel.needDownloadUrls();
      if (urlList.isEmpty) {
        l.i('TeamStudyTag', '没有需要下载的资源');
        return true;
      }

      GlobalDownloadManager downloadManager = GlobalDownloadManager();
      Map<String, String> result =
          await downloadManager.downloadResources(urlList, needUnzip: true);
      l.i('TeamStudyTag', '资源下载成功: $result');
      TeamStudyResourceTool.saveResourceMap(result, activityModel.resource);
      return true;
    } catch (e) {
      l.i('TeamStudyTag', '资源下载失败:$e');
      return false;
    }
  }

  // 解析资源配置
  static Future<TeamStudyResourceConfig> parseResourceConfig() async {
    final json = await TeamStudyResourceTool.getResourceConfig();
    return TeamStudyResourceConfig.fromJson(json);
  }

  static saveResourceMap(Map<String,String> map,String? url) {
    resourceMap = map;
    resourceUrl = url ?? '';
  }

  static clearResource(){
    resourceMap = {};
  }

  static String getLocalPath(String url) {
    final path = resourceMap[url];
    return path ?? '';
  }

  static String getResourceConfigPath() {
    final dir = getLocalPath(resourceUrl);
    final fullPath = '$dir/team_study_resource/config.json';
    return fullPath;
  }

  static Future<Map<String, dynamic>> getResourceConfig() async {
    try {
      final fullPath = getResourceConfigPath();
      if(fullPath.isEmpty){
        return {};
      }
      final file = File(fullPath); // 构建文件对象
      if (!await file.exists()) {
        throw Exception('Resource config file not found: $fullPath');
      }
      String jsonString = await file.readAsString(); // 读取文件内容
      final jsonMap = json.decode(jsonString);
      return jsonMap as Map<String, dynamic>;
    } catch (e) {
      // 处理文件读取错误
      throw Exception('Failed to read resource config: $e');
    }
  }

  // 获取spine路径
  static String getSpinePath(String name) {
    final dir = getLocalPath(resourceUrl);
    final fullPath = '$dir/team_study_resource/spine/$name';
    return fullPath;
  }

  // 获取图片路径
  static String getImagePath(String name){
      final dir = getLocalPath(resourceUrl);
    final fullPath = '$dir/team_study_resource/image/$name';
    return fullPath;
  }

  // 获取音频路径
  static String getAudioPath(String name){
      final dir = getLocalPath(resourceUrl);
    final fullPath = '$dir/team_study_resource/audio/$name';
    return fullPath;
  }
}
